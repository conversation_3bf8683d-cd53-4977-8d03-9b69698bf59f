@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --gradient-stop-1: 15.1% 15.1% at 50% 50%, #0F0114 0%, rgba(15, 1, 20, 0) 100%;
    --gradient-stop-2: 84.9% 84.9% at 50% 50%, #2D0A3D 0%, rgba(45, 10, 61, 0) 100%;
    --neon-accent: 192 70% 62%;
  }

  /* Base element styling */
  body {
    @apply bg-gradient-to-br from-[var(--gradient-stop-1)] to-[var(--gradient-stop-2)] text-gray-100;
  }

  h1 {
    @apply text-4xl font-bold tracking-tighter;
  }
}

@layer utilities {
  /* Custom gradient utilities */
  .cyber-gradient {
    @apply bg-[linear-gradient(45deg,#701DAD_0%,#A245E9_100%)];
  }

  .neon-glow {
    @apply shadow-[0_0_15px_rgba(192,70,247,0.4)];
  }

  /* Animation utilities */
  .animate-gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent;
    animation: gradient-text 3s ease infinite;
  }
}

@layer components {
  /* Interactive Button Component */
  .cyber-button {
    @apply relative overflow-hidden px-8 py-3 rounded-lg 
           border-2 border-transparent bg-opacity-10
           transition-all duration-300 hover:bg-opacity-20
           focus:outline-none focus:neon-glow;
    
    &::before {
      @apply content-[''] absolute inset-0 cyber-gradient opacity-0 
             transition-opacity duration-300;
    }
    
    &:hover::before {
      @apply opacity-100;
    }
  }

  /* Card Component with 3D Effect */
  .cyber-card {
    @apply relative bg-white/5 backdrop-blur-lg rounded-xl p-6
           transition-transform duration-300 hover:scale-[1.02]
           border border-white/10 hover:border-purple-400/30;
    
    .card-header {
      @apply mb-4 pb-2 border-b border-white/10;
    }
  }

  /* Validation-aware Input Group */
  .cyber-input {
    @apply bg-black/20 border border-white/10 rounded-lg px-4 py-3
           text-sm placeholder:text-gray-400 focus:ring-2 
           focus:ring-purple-500 focus:border-transparent
           transition-all duration-200;
    
    &:invalid {
      @apply border-red-400/30 focus:ring-red-400/50;
    }
  }
}

/* Animations */
@keyframes gradient-text {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}