# Shinmen AI Design System

## 1. Design Tokens

### Color Palette
```css
/* styles/tokens.css */
:root {
  --gradient-deep-space: linear-gradient(
    135deg,
    #0F0114 0%,
    #2D0A3D 50%,
    #4A1261 100%
  );
  
  --neon-accent: #C046F7;
  --cyber-border: linear-gradient(45deg, #701DAD 0%, #A245E9 100%);
}

/* Glassmorphism effect */
.glass-panel {
  backdrop-filter: blur(16px);
  background: rgba(15, 1, 20, 0.2);
  border: 1px solid var(--neon-accent);
}
```

### Typography Scale
```css
/* Fluid typography */
:root {
  --text-hero: clamp(2.5rem, 8vw + 1rem, 5rem);
  --text-body: clamp(1rem, 0.34vw + 0.91rem, 1.19rem);
}

@font-face {
  font-family: 'Space Grotesk';
  src: url('/fonts/SpaceGrotesk.woff2') format('woff2');
  font-display: swap;
}
```

## 2. Core Components

### CyberButton
```tsx
// components/ui/CyberButton.tsx
export const CyberButton = () => (
  <button className="relative bg-gradient-to-br from-[#701DAD] to-[#A245E9] p-[2px]">
    <div className="bg-[#0F0114] px-8 py-4 transition-all hover:bg-opacity-50">
      <span className="bg-gradient-to-r from-[#C046F7] to-white bg-clip-text text-transparent">
        Engage AI
      </span>
    </div>
  </button>
);
```

## 3. Performance Strategies

### WebGL Optimization Pipeline
1. **Asset Compression**:
   - GLB files ≤ 500KB
   - Draco compression for geometry
   - Basis Universal textures

2. **Lazy Loading**:
```tsx
const AIOrb = dynamic(
  () => import('@/components/AIOrb'),
  { 
    ssr: false,
    loading: () => <Skeleton className="h-[400px] w-[400px]" />
  }
)
```

## 4. Accessibility

### Motion Guidelines
```css
@media (prefers-reduced-motion: reduce) {
  .cyber-glows {
    animation: none;
    opacity: 0.8;
  }
}
```

### Contrast Ratios
| Element          | Minimum Ratio | Current Value |
|------------------|---------------|---------------|
| Primary Text     | 4.5:1         | 5.2:1         |
| Interactive      | 3:1           | 3.8:1         |

## 5. Figma Sync

### Variable Mapping
```json
{
  "colors": {
    "deep-space-100": "#0F0114",
    "neon-accent": "#C046F7"
  },
  "typography": {
    "hero": {"fontSize": "5rem", "lineHeight": "1.1"}
  }
}
```

Next phase recommendations:
1. Implement global layout component with theme provider
2. Set up WebGL context wrapper
3. Configure API security middleware